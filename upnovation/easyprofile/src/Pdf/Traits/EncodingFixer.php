<?php namespace Upnovation\Easyprofile\Pdf\Traits;

trait EncodingFixer
{
    /**
     * Fix encoding issues for FPDF compatibility
     * Handles cases where text like "Aglié" becomes "AgliÃ¨" due to double-encoding
     * 
     * @param string $text The text to fix
     * @return string The text properly encoded for FPDF (ISO-8859-1)
     */
    protected function fixEncoding($text)
    {
        if (empty($text)) {
            return $text;
        }
        
        if (mb_check_encoding($text, 'UTF-8')) {
            return mb_convert_encoding($text, 'ISO-8859-1', 'UTF-8');
        }
        
        // Return as-is if already in correct encoding
        return $text;
    }

    /**
     * Apply encoding fix to text before writing to PDF
     * This is a convenience method that can be used directly before PDF write operations
     * 
     * @param string $text The text to prepare for PDF output
     * @return string The text ready for FPDF output
     */
    protected function prepareTextForPdf($text)
    {
        return $this->fixEncoding($text);
    }
}
